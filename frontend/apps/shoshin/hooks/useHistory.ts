"use client"

import { useCallback, useRef, useState } from "react"
import type { Node, Edge } from "@xyflow/react"

interface HistoryState {
  nodes: Node[]
  edges: Edge[]
  timestamp: number
}

interface HistoryActions {
  undo: () => boolean
  redo: () => boolean
  canUndo: boolean
  canRedo: boolean
  saveState: (nodes: Node[], edges: Edge[]) => void
  clearHistory: () => void
}

const MAX_HISTORY_SIZE = 50

export function useHistory(
  nodes: Node[],
  edges: Edge[],
  setNodes: (nodes: Node[]) => void,
  setEdges: (edges: Edge[]) => void
): HistoryActions {
  const undoStack = useRef<HistoryState[]>([])
  const redoStack = useRef<HistoryState[]>([])
  const [canUndo, setCanUndo] = useState(false)
  const [canRedo, setCanRedo] = useState(false)

  const updateFlags = useCallback(() => {
    setCanUndo(undoStack.current.length > 0)
    setCanRedo(redoStack.current.length > 0)
  }, [])

  const saveState = useCallback((currentNodes: Node[], currentEdges: Edge[]) => {
    // Deep clone the current state
    const state: HistoryState = {
      nodes: JSON.parse(JSON.stringify(currentNodes)),
      edges: JSON.parse(JSON.stringify(currentEdges)),
      timestamp: Date.now()
    }

    undoStack.current.push(state)
    
    // Limit history size
    if (undoStack.current.length > MAX_HISTORY_SIZE) {
      undoStack.current.shift()
    }

    // Clear redo stack when new action is performed
    redoStack.current = []
    updateFlags()
  }, [updateFlags])

  const undo = useCallback((): boolean => {
    if (undoStack.current.length === 0) return false

    // Save current state to redo stack
    const currentState: HistoryState = {
      nodes: JSON.parse(JSON.stringify(nodes)),
      edges: JSON.parse(JSON.stringify(edges)),
      timestamp: Date.now()
    }
    redoStack.current.push(currentState)

    // Restore previous state
    const previousState = undoStack.current.pop()!
    setNodes(previousState.nodes)
    setEdges(previousState.edges)

    updateFlags()
    return true
  }, [nodes, edges, setNodes, setEdges, updateFlags])

  const redo = useCallback((): boolean => {
    if (redoStack.current.length === 0) return false

    // Save current state to undo stack
    const currentState: HistoryState = {
      nodes: JSON.parse(JSON.stringify(nodes)),
      edges: JSON.parse(JSON.stringify(edges)),
      timestamp: Date.now()
    }
    undoStack.current.push(currentState)

    // Restore next state
    const nextState = redoStack.current.pop()!
    setNodes(nextState.nodes)
    setEdges(nextState.edges)

    updateFlags()
    return true
  }, [nodes, edges, setNodes, setEdges, updateFlags])

  const clearHistory = useCallback(() => {
    undoStack.current = []
    redoStack.current = []
    updateFlags()
  }, [updateFlags])

  return {
    undo,
    redo,
    canUndo,
    canRedo,
    saveState,
    clearHistory
  }
}
