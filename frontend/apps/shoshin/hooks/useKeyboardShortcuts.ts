"use client"

import { useCallback, useEffect } from "react"

interface KeyboardShortcutActions {
  onUndo?: () => void
  onRedo?: () => void
  onCopy?: () => void
  onCut?: () => void
  onPaste?: () => void
  onDelete?: () => void
}

interface KeyboardShortcutOptions {
  disabled?: boolean
}

export function useKeyboardShortcuts(
  actions: KeyboardShortcutActions,
  options: KeyboardShortcutOptions = {}
) {
  const isInputFocused = useCallback((): boolean => {
    const activeElement = document.activeElement
    if (!activeElement) return false

    const tagName = activeElement.tagName.toLowerCase()
    const isInput = ['input', 'textarea', 'select'].includes(tagName)
    const isContentEditable = activeElement.getAttribute('contenteditable') === 'true'
    
    return isInput || isContentEditable
  }, [])

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Don't handle shortcuts if disabled or if an input is focused
    if (options.disabled || isInputFocused()) {
      return
    }

    const { ctrlKey, metaKey, shiftKey, key } = event
    const isCtrlOrCmd = ctrlKey || metaKey

    // Prevent default browser behavior for our shortcuts
    let shouldPreventDefault = false

    if (isCtrlOrCmd) {
      switch (key.toLowerCase()) {
        case 'z':
          if (shiftKey) {
            // Ctrl+Shift+Z or Ctrl+Y for redo
            actions.onRedo?.()
          } else {
            // Ctrl+Z for undo
            actions.onUndo?.()
          }
          shouldPreventDefault = true
          break

        case 'y':
          // Ctrl+Y for redo (alternative to Ctrl+Shift+Z)
          actions.onRedo?.()
          shouldPreventDefault = true
          break

        case 'c':
          // Ctrl+C for copy
          actions.onCopy?.()
          shouldPreventDefault = true
          break

        case 'x':
          // Ctrl+X for cut
          actions.onCut?.()
          shouldPreventDefault = true
          break

        case 'v':
          // Ctrl+V for paste
          actions.onPaste?.()
          shouldPreventDefault = true
          break
      }
    }

    // Handle Delete/Backspace for deletion
    if (key === 'Delete' || key === 'Backspace') {
      actions.onDelete?.()
      shouldPreventDefault = true
    }

    if (shouldPreventDefault) {
      event.preventDefault()
      event.stopPropagation()
    }
  }, [actions, options.disabled, isInputFocused])

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleKeyDown])

  return {
    isInputFocused
  }
}
