"use client"

import {
  Background,
  Controls,
  MarkerType,
  MiniMap,
  ReactFlow,
  ReactFlowProvider,
  addEdge,
  useEdgesState,
  useNodesState,

  type Edge,
  type Node,
  type OnConnect,
  type OnSelectionChangeParams,
} from "@xyflow/react"
import "@xyflow/react/dist/style.css"
import { useCallback, useEffect, useRef, useState } from "react"
import { useClipboard } from "../../hooks/useClipboard"
import { useHistory } from "../../hooks/useHistory"
import { useKeyboardShortcuts } from "../../hooks/useKeyboardShortcuts"
import { useSelection } from "../../hooks/useSelection"
import { useToast } from "../../hooks/useToast"
import { ToastContainer } from "../ui/Toast"
import { CustomNode } from "./CustomNode"
import { ShoshinEdge } from "./ShoshinEdge"

const nodeTypes = {
  custom: CustomNode,
}

const edgeTypes = {
  shoshin: ShoshinEdge,
}

const initialNodes: Node[] = [
  {
    id: "start-1",
    type: "custom",
    position: { x: 250, y: 100 },
    data: { 
      label: "Start",
      type: "start",
      description: "Start Workflow"
    },
  },
]

const initialEdges: Edge[] = []

function MainCanvasInner() {
  const reactFlowWrapper = useRef<HTMLDivElement>(null)
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)
  const [reactFlowInstance, setReactFlowInstance] = useState<any>(null)

  // Refs to access current state without dependencies
  const nodesRef = useRef(nodes)
  const edgesRef = useRef(edges)

  // Update refs when state changes
  useEffect(() => {
    nodesRef.current = nodes
  }, [nodes])

  useEffect(() => {
    edgesRef.current = edges
  }, [edges])

  // Initialize hooks
  const selection = useSelection()
  const clipboard = useClipboard()
  const { toasts, showToast, removeToast } = useToast()

  // History management
  const history = useHistory(setNodes, setEdges)



  // Selection change handler
  const onSelectionChange = useCallback((params: OnSelectionChangeParams) => {
    selection.updateSelection(params.nodes, params.edges)
  }, [selection])

  // Delete selected nodes and edges
  const deleteSelected = useCallback(() => {
    const selectedNodeIds = selection.selectedNodes.map(node => node.id)
    const selectedEdgeIds = selection.selectedEdges.map(edge => edge.id)

    if (selectedNodeIds.length === 0 && selectedEdgeIds.length === 0) {
      showToast("No items selected to delete", "warning")
      return
    }

    // Save current state before making changes
    history.saveState(nodesRef.current, edgesRef.current)

    // Remove selected nodes
    if (selectedNodeIds.length > 0) {
      setNodes(nodes => nodes.filter(node => !selectedNodeIds.includes(node.id)))
    }

    // Remove selected edges and any edges connected to deleted nodes
    setEdges(edges => edges.filter(edge =>
      !selectedEdgeIds.includes(edge.id) &&
      !selectedNodeIds.includes(edge.source) &&
      !selectedNodeIds.includes(edge.target)
    ))

    const itemCount = selectedNodeIds.length + selectedEdgeIds.length
    showToast(`Deleted ${itemCount} item${itemCount > 1 ? 's' : ''}`, "success")
  }, [selection, setNodes, setEdges, history.saveState, showToast])

  // Clipboard operations
  const copySelected = useCallback(() => {
    if (!selection.hasSelection) {
      showToast("No items selected to copy", "warning")
      return
    }

    clipboard.copy(selection.selectedNodes, selection.selectedEdges)
    const itemCount = selection.selectedNodes.length + selection.selectedEdges.length
    showToast(`Copied ${itemCount} item${itemCount > 1 ? 's' : ''}`, "success")
  }, [selection, clipboard, showToast])

  const cutSelected = useCallback(() => {
    if (!selection.hasSelection) {
      showToast("No items selected to cut", "warning")
      return
    }

    const selectedNodeIds = selection.selectedNodes.map(node => node.id)
    const selectedEdgeIds = selection.selectedEdges.map(edge => edge.id)

    // Save state before cutting
    history.saveState(nodesRef.current, edgesRef.current)

    clipboard.cut(selection.selectedNodes, selection.selectedEdges, (nodeIds, edgeIds) => {
      setNodes(nodes => nodes.filter(node => !nodeIds.includes(node.id)))
      setEdges(edges => edges.filter(edge =>
        !edgeIds.includes(edge.id) &&
        !nodeIds.includes(edge.source) &&
        !nodeIds.includes(edge.target)
      ))
    })

    const itemCount = selectedNodeIds.length + selectedEdgeIds.length
    showToast(`Cut ${itemCount} item${itemCount > 1 ? 's' : ''}`, "success")
  }, [selection, clipboard, setNodes, setEdges, history.saveState, showToast])

  const pasteFromClipboard = useCallback(() => {
    if (!clipboard.hasClipboardData) {
      showToast("Nothing to paste", "warning")
      return
    }

    // Get viewport center for paste position
    const viewportCenter = reactFlowInstance?.getViewport()
    const pastePosition = viewportCenter ? {
      x: -viewportCenter.x / viewportCenter.zoom + 400,
      y: -viewportCenter.y / viewportCenter.zoom + 300
    } : undefined

    const result = clipboard.paste(pastePosition)
    if (!result) {
      showToast("Failed to paste items", "error")
      return
    }

    // Save state before pasting
    history.saveState(nodesRef.current, edgesRef.current)
    setNodes(nodes => [...nodes, ...result.nodes])
    setEdges(edges => [...edges, ...result.edges])

    const itemCount = result.nodes.length + result.edges.length
    showToast(`Pasted ${itemCount} item${itemCount > 1 ? 's' : ''}`, "success")
  }, [clipboard, reactFlowInstance, setNodes, setEdges, history.saveState, showToast])

  // Keyboard shortcuts
  useKeyboardShortcuts({
    onUndo: () => {
      if (history.undo(nodesRef.current, edgesRef.current)) {
        showToast("Undone", "info")
      } else {
        showToast("Nothing to undo", "warning")
      }
    },
    onRedo: () => {
      if (history.redo(nodesRef.current, edgesRef.current)) {
        showToast("Redone", "info")
      } else {
        showToast("Nothing to redo", "warning")
      }
    },
    onCopy: copySelected,
    onCut: cutSelected,
    onPaste: pasteFromClipboard,
    onDelete: deleteSelected
  })

  // Auto layout function
  const applyAutoLayout = useCallback(() => {
    const currentNodes = nodesRef.current
    const currentEdges = edgesRef.current

    if (currentNodes.length === 0) return

    // Create adjacency list to understand node relationships
    const adjacencyList = new Map<string, string[]>()
    const inDegree = new Map<string, number>()

    // Initialize
    currentNodes.forEach(node => {
      adjacencyList.set(node.id, [])
      inDegree.set(node.id, 0)
    })

    // Build graph
    currentEdges.forEach(edge => {
      if (edge.source && edge.target) {
        adjacencyList.get(edge.source)?.push(edge.target)
        inDegree.set(edge.target, (inDegree.get(edge.target) || 0) + 1)
      }
    })

    // Find root nodes (nodes with no incoming edges)
    const rootNodes = currentNodes.filter(node => inDegree.get(node.id) === 0)

    // If no root nodes, use the first node as root
    if (rootNodes.length === 0 && currentNodes.length > 0) {
      rootNodes.push(currentNodes[0])
    }

    // Layout configuration
    const HORIZONTAL_SPACING = 300
    const VERTICAL_SPACING = 150
    const START_X = 100
    const START_Y = 100

    // Track positioned nodes and their levels
    const positioned = new Set<string>()
    const levels = new Map<string, number>()
    const levelNodes = new Map<number, string[]>()

    // BFS to assign levels
    const queue: Array<{ nodeId: string; level: number }> = []

    rootNodes.forEach(node => {
      queue.push({ nodeId: node.id, level: 0 })
      levels.set(node.id, 0)
    })

    while (queue.length > 0) {
      const { nodeId, level } = queue.shift()!

      if (positioned.has(nodeId)) continue
      positioned.add(nodeId)

      // Add to level tracking
      if (!levelNodes.has(level)) {
        levelNodes.set(level, [])
      }
      levelNodes.get(level)!.push(nodeId)

      // Add children to queue
      const children = adjacencyList.get(nodeId) || []
      children.forEach(childId => {
        if (!positioned.has(childId)) {
          const childLevel = level + 1
          levels.set(childId, childLevel)
          queue.push({ nodeId: childId, level: childLevel })
        }
      })
    }

    // Position nodes based on levels
    const updatedNodes = currentNodes.map(node => {
      const level = levels.get(node.id) || 0
      const nodesInLevel = levelNodes.get(level) || []
      const indexInLevel = nodesInLevel.indexOf(node.id)

      // Center nodes in each level
      const totalNodesInLevel = nodesInLevel.length
      const levelWidth = (totalNodesInLevel - 1) * HORIZONTAL_SPACING
      const startXForLevel = START_X - levelWidth / 2

      return {
        ...node,
        position: {
          x: startXForLevel + indexInLevel * HORIZONTAL_SPACING,
          y: START_Y + level * VERTICAL_SPACING
        }
      }
    })

    setNodes(updatedNodes)
  }, [setNodes])

  // Listen for auto layout events
  useEffect(() => {
    const handleAutoLayout = () => {
      applyAutoLayout()
    }

    window.addEventListener('trigger-auto-layout', handleAutoLayout)
    return () => {
      window.removeEventListener('trigger-auto-layout', handleAutoLayout)
    }
  }, [applyAutoLayout])

  const onConnect: OnConnect = useCallback(
    (params) => {
      history.saveState(nodesRef.current, edgesRef.current)
      setEdges((eds) => addEdge(params, eds))
    },
    [setEdges, history.saveState]
  )

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = "move"
  }, [])

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault()

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect()
      const data = event.dataTransfer.getData("application/reactflow")

      if (typeof data === "undefined" || !data || !reactFlowBounds) {
        return
      }

      const blockData = JSON.parse(data)
      const position = reactFlowInstance?.screenToFlowPosition({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      })

      const newNode: Node = {
        id: `${blockData.type}-${Date.now()}`,
        type: "custom",
        position,
        data: {
          label: blockData.name,
          type: blockData.type,
          description: blockData.description,
        },
      }

      history.saveState(nodesRef.current, edgesRef.current)
      setNodes((nds) => nds.concat(newNode))
    },
    [reactFlowInstance, setNodes, history.saveState]
  )

  return (
    <div className="w-full h-full" ref={reactFlowWrapper}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onInit={setReactFlowInstance}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onSelectionChange={onSelectionChange}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        className="bg-gray-900"
        defaultEdgeOptions={{
          type: 'shoshin',
          animated: true,
          markerEnd: {
            type: MarkerType.ArrowClosed,
            color: '#6366f1',
          },
        }}
        connectionLineStyle={{
          stroke: '#6366f1',
          strokeWidth: 2,
          strokeDasharray: '5,5'
        }}
        snapToGrid={true}
        snapGrid={[15, 15]}
      >
        <Background
          color="#374151"
          gap={20}
          size={1}
        />
        <Controls
          className="bg-gray-800 border-gray-600 [&>button]:bg-gray-700 [&>button]:border-gray-600 [&>button]:text-gray-300 [&>button:hover]:bg-gray-600"
        />
        <MiniMap
          className="bg-gray-800 border-gray-600"
          nodeColor="#6366f1"
          maskColor="rgba(0, 0, 0, 0.3)"
          pannable
          zoomable
        />
      </ReactFlow>
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </div>
  )
}

export function MainCanvas() {
  return (
    <ReactFlowProvider>
      <MainCanvasInner />
    </ReactFlowProvider>
  )
}
